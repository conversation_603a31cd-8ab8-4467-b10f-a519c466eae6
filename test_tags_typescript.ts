/**
 * TypeScript test to verify that tags support has been added to braintrust.init()
 * This file tests the type definitions without needing to run the code
 */

import * as braintrust from "./js/src/index";

// Test 1: Basic tags usage
async function testBasicTags() {
    const experiment = await braintrust.init("test-project", {
        experiment: "test-experiment",
        tags: ["test-tag", "experiment-tag"], // This should compile without errors
        apiKey: "fake-key"
    });
}

// Test 2: Tags with other options
async function testTagsWithOtherOptions() {
    const experiment = await braintrust.init({
        project: "test-project",
        experiment: "test-experiment",
        description: "Test experiment with tags",
        tags: ["production", "model-v2"], // This should compile without errors
        metadata: { version: "1.0" },
        isPublic: false
    });
}

// Test 3: Optional tags (should work with undefined)
async function testOptionalTags() {
    const experiment = await braintrust.init("test-project", {
        experiment: "test-experiment",
        tags: undefined, // This should compile without errors
    });
}

// Test 4: Empty tags array
async function testEmptyTags() {
    const experiment = await braintrust.init("test-project", {
        experiment: "test-experiment", 
        tags: [], // This should compile without errors
    });
}

console.log("✅ TypeScript compilation test passed - tags parameter is properly typed!");
